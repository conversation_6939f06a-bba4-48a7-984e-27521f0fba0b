import logging
from typing import List

from tortoise.exceptions import DoesNotExist, BaseORMException
from tortoise.expressions import Q

from app.models.tortoise import User, Connection
from app.utils.minio_service import MinioService

log = logging.getLogger(__name__)
profile_pictures_bucket = MinioService("user-profile-pictures")


async def get_user(user_id: str):
    """ Get a user by their ID """
    try:
        user = await User.get(id=user_id)
        return user
    except DoesNotExist:
        return None


async def update_user_refresh_token(user_id: str, new_refresh_token: str):
    """ Update the refresh token of a user """
    try:
        user = await User.get(id=user_id)
        user.refresh_token = new_refresh_token
        await user.save()
        return True
    except DoesNotExist:
        return False

async def check_user_exists(oauth_user_id: str) -> bool:
    try:
        user_exists = await User.filter(oauth_user_id=oauth_user_id).exists()
        return user_exists
    except BaseORMException as err:
        log.error(err)
        raise ValueError('An unexpected error occurred when accessing the database.')


async def get_user_by_oauth_id(oauth_user_id: str):
    """ Get a user by their immutable OAuth ID """
    try:
        user = await User.get(oauth_user_id=oauth_user_id)
        return user
    except DoesNotExist:
        return None


async def create_user(oauth_user_id: str, email: str, oauth_provider: str, music_provider: str):
    """ Create a new user """
    try:
        new_user = User(
            email=email,
            oauth_user_id=oauth_user_id,
            oauth_provider=oauth_provider,
            music_provider=music_provider

        )
        await new_user.save()

        if new_user is None:
            log.error(f"Failed to create user with email: {email}")
            return None

    except Exception as err:
        log.exception("Exception occurred while creating user and OAuth", exc_info=err)
        return None

    return new_user


async def update_user_profile(user_id: int, user_profile_data) -> bool:
    """ Update the profile of a user """
    try:
        user = await User.get(id=user_id)
        if user_profile_data.firstName:
            user.first_name = user_profile_data.firstName
        if user_profile_data.lastName:
            user.last_name = user_profile_data.lastName
        if user_profile_data.username:
            user.username = user_profile_data.username.strip()
        if user_profile_data.musicProvider:
            user.music_provider = user_profile_data.musicProvider
        if user_profile_data.bio:
            user.bio = user_profile_data.bio
        await user.save()
        return True
    except DoesNotExist:
        return False


async def get_user_refresh_token(user_id: str):
    """ Get the refresh token of a user """
    try:
        user = await User.get(id=user_id)
        return user.refresh_token
    except DoesNotExist:
        return None


async def update_user_refresh_token(user_id: str, new_refresh_token: str):
    """ Update the refresh token of a user """
    try:
        user = await User.get(id=user_id)
        user.refresh_token = new_refresh_token
        await user.save()
        return True
    except DoesNotExist:
        return False


async def check_username_available(username: str):
    """ Check if a username is available """
    try:
        user = await User.get(username=username.lower().strip())
        return False
    except DoesNotExist:
        return True


async def delete_user(user_id):
    """ Delete a user """
    try:
        user = await User.get(id=user_id)
        await user.delete()
        return True
    except DoesNotExist:
        return False


async def update_user_expo_rpn_token(user_id: str, expo_rpn_token: str):
    """ Update the Expo RPN token of a user """
    try:
        user = await User.get(id=user_id)
        user.expo_rpn_token = expo_rpn_token
        await user.save()
        return True
    except DoesNotExist:
        return False


async def search_users(query: str, user_id: str):
    """ Search for users based on a query """
    query = query.lower()
    try:
        # Step 1: Retrieve all connections for the user
        connections = await Connection.filter(
            Q(initiator_user_id=user_id) | Q(recipient_user_id=user_id)
        ).all()

        # Step 2: Create a set of user IDs to exclude (including the user's own ID)
        exclude_user_ids = {user_id}
        for connection in connections:
            exclude_user_ids.add(connection.initiator_user_id)
            exclude_user_ids.add(connection.recipient_user_id)

        # Step 3: Filter users based on the query, excluding the user and their connections
        users = await User.filter(
            username__icontains=query
        ).exclude(
            id__in=exclude_user_ids
        ).all()

        return users
    except Exception as err:
        log.error(err)
        raise ValueError('An unexpected error occurred when accessing the database.')


async def get_all_expo_rpn_tokens() -> List[str]:
    tokens = await User.filter(expo_rpn_token__isnull=False).values_list('expo_rpn_token', flat=True)
    return tokens


async def get_all_users():
    """ Get all users from the database """
    try:
        users = await User.all()
        return users
    except Exception as err:
        log.error(f"Error fetching all users: {err}")
        raise ValueError('An unexpected error occurred when accessing the database.')
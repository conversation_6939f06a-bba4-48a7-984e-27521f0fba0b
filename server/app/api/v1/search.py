from fastapi import <PERSON><PERSON><PERSON><PERSON>, Depends, HTTPException
from fastapi.responses import JSONResponse

from app.api.v1.crud import users as crud_users
from app.api.v1.pydantic import SearchQueryParams
from app.utils.auth.auth_bearer import J<PERSON><PERSON><PERSON>earer
from app.utils.auth.auth_handler import decodeJWT
from app.utils.search import apple_music
from app.utils.search.meilisearch import meilisearch_client

router = APIRouter(prefix="/api/v1/search", tags=["SEARCH-V1"])


@router.get("/music/apple")
async def search_apple_music(params: SearchQueryParams = Depends()):
    try:
        token, token_expiry = await apple_music.generate_developer_token()
        response = await apple_music.search_music(term=params.query, token=token)

        return response
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))



@router.get("/users", dependencies=[Depends(JWTBearer())])
async def search_users_with_meilisearch(params: SearchQueryParams = Depends(), token: str = Depends(JWTBearer())):
    """
    Search users using Meilisearch for fast and relevant results.
    Searches across username, first_name, last_name, and bio fields.
    """
    #TODO: remove the users that are already connected to the user + users who are private + users who are not verified + users who blocked the requesting user
    try:
        user_id = decodeJWT(token).get('user_id')

        # Use the singleton meilisearch client to search
        search_results = meilisearch_client.search_users(
            query=params.query,
            limit=params.limit,
            offset=params.offset,
            exclude_user_id=user_id
        )

        if not search_results or not search_results.get('hits'):
            return JSONResponse(
                content={
                    "users": [],
                    "total": 0,
                    "query": params.query,
                    "offset": params.offset,
                    "limit": params.limit
                },
                status_code=200
            )

        return JSONResponse(
            content={
                "users": search_results['hits'],
                "total": search_results.get('estimatedTotalHits', 0),
                "query": params.query,
                "offset": params.offset,
                "limit": params.limit,
                "processing_time_ms": search_results.get('processingTimeMs', 0)
            },
            status_code=200
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Search failed: {str(e)}")
from fastapi import APIRouter, Depends, HTTPException, JSONResponse

from app.api.v1.crud import users as crud_users
from app.api.v1.pydantic import SearchQueryParams
from app.utils.auth.auth_bearer import JWTBearer
from app.utils.auth.auth_handler import decodeJWT
from app.utils.search import apple_music
import meilisearch


router = APIRouter(prefix="/api/v1/search", tags=["SEARCH-V1"])
client = meilisearch.Client('', 'axN1BCyDqoJ3yCRTTrNkjtWWSMvrkmSvAqXUg3yejig')


@router.get("/music/apple")
async def search_apple_music(params: SearchQueryParams = Depends()):
    try:
        token, token_expiry = await apple_music.generate_developer_token()
        response = await apple_music.search_music(term=params.query, token=token)

        return response
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/users/{username}", dependencies=[Depends(JWTBearer())])
async def search_users_by_username(username: str, token: str = Depends(JWTBearer())):
    user_id = decodeJWT(token).get('user_id')
    users = await crud_users.search_users(username, user_id)
    if not users:
        raise HTTPException(status_code=404, detail="No users found")
    return users


@router.get("/users", dependencies=[Depends(JWTBearer())])
async def search_users_by_query(params: SearchQueryParams = Depends(), token: str = Depends(JWTBearer())):
    user_id = decodeJWT(token).get('user_id')
    users = await crud_users.search_users(params.query, user_id)
    if not users:
        raise HTTPException(status_code=404, detail="No users found")

    index = client.index('users')
    results = index.search(params.query, {
        'limit': 25,
        'attributesToHighlight': ['username']
    })
    print(results)

    #TODO: remove the users that are already connected to the user + users who are private + users who are not verified + users who blocked the requesting user

    return JSONResponse(status_code=200, content=results)

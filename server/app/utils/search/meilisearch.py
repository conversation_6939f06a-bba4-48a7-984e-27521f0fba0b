import logging
from typing import List, Dict, Any
import meilisearch
from app.config import get_settings

log = logging.getLogger(__name__)


class MeilisearchClient:
    def __init__(self):
        settings = get_settings()
        self.client = meilisearch.Client(
            url=settings.meilisearch_url,
            api_key=settings.meilisearch_api_key
        )
    
    async def create_users_index(self) -> bool:
        """
        Create or update the users index with proper configuration
        """
        try:
            index_name = "users"
            
            # Create or get the index
            try:
                index = self.client.get_index(index_name)
                log.info(f"Users index '{index_name}' already exists")
            except meilisearch.errors.MeilisearchApiError as e:
                if e.code == "index_not_found":
                    # Create the index
                    task = self.client.create_index(index_name, {"primaryKey": "user_id"})
                    self.client.wait_for_task(task.task_uid)
                    index = self.client.get_index(index_name)
                    log.info(f"Created users index '{index_name}'")
                else:
                    raise e
            
            # Configure searchable attributes - only username should be searchable
            searchable_attributes = [
                "username"
            ]
            
            # Configure filterable attributes
            filterable_attributes = [
                "user_id"
            ]
            
            # Update index settings
            task = index.update_searchable_attributes(searchable_attributes)
            self.client.wait_for_task(task.task_uid)
            
            task = index.update_filterable_attributes(filterable_attributes)
            self.client.wait_for_task(task.task_uid)
            
            log.info("Users index configuration updated successfully")
            return True
            
        except Exception as e:
            log.error(f"Error creating/configuring users index: {str(e)}")
            return False
    
    async def index_users(self, users_data: List[Dict[str, Any]]) -> bool:
        """
        Index users data in Meilisearch
        """
        try:
            index = self.client.get_index("users")
            
            # Add documents to the index
            task = index.add_documents(users_data)
            self.client.wait_for_task(task.task_uid)
            
            log.info(f"Successfully indexed {len(users_data)} users")
            return True
            
        except Exception as e:
            log.error(f"Error indexing users: {str(e)}")
            return False
    
    async def clear_users_index(self) -> bool:
        """
        Clear all documents from the users index
        """
        try:
            index = self.client.get_index("users")
            task = index.delete_all_documents()
            self.client.wait_for_task(task.task_uid)

            log.info("Users index cleared successfully")
            return True

        except Exception as e:
            log.error(f"Error clearing users index: {str(e)}")
            return False

    async def search_users(self, query: str, limit: int = 20, offset: int = 0, exclude_user_id: str = None) -> Dict[str, Any]:
        """
        Search users in Meilisearch
        """
        try:
            index = self.client.get_index("users")

            # Build search parameters
            search_params = {
                "limit": limit,
                "offset": offset,
                "attributesToRetrieve": [
                    "user_id",
                    "username",
                    "first_name",
                    "last_name",
                    "bio",
                    "profile_picture"
                ]
            }

            # Add filter to exclude the current user if provided
            if exclude_user_id:
                search_params["filter"] = f"user_id != {exclude_user_id}"

            # Perform the search
            results = index.search(query, search_params)

            log.info(f"Meilisearch user search for '{query}' returned {len(results.get('hits', []))} results")
            return results

        except Exception as e:
            log.error(f"Error searching users: {str(e)}")
            return {"hits": [], "estimatedTotalHits": 0, "processingTimeMs": 0}


# Create a singleton instance
meilisearch_client = MeilisearchClient()
